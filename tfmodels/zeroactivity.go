package tfmodels

type ZeroActivity struct {
	ID                int64  `gorm:"column:id;primaryKey"`       // 外呼任务ID
	ActyName          string `gorm:"column:acty_name"`           // 活动名字
	AppID             int64  `gorm:"column:app_id"`              // 应用方ID
	PromoteBusiness   int    `gorm:"column:promote_business"`    // 业务线
	PromoteGradeDepts string `gorm:"column:promote_grade_depts"` // 学部
	PromoteLv1News    string `gorm:"column:promote_lv1_news"`    // 新推广产品
	ChannelLabels     string `gorm:"column:channel_labels"`      // 渠道标签
	RuleType          int    `gorm:"column:rule_type"`
	InstIdList        string `gorm:"column:inst_id_list"`                         // 代理商机构白名单
	ClassId           int    `gorm:"column:class_id"`                             // 群控渠道大类id
	ChannelNameAbbr   string `gorm:"column:channel_name_abbr"`                    // 渠道名称简称
	ActType           int    `gorm:"column:act_type"`                             // 活动类型：1-群控魔方，2-群控直投
	TaskID            int    `gorm:"column:task_id"`                              // 群控h5任务ID
	TaskName          string `gorm:"column:task_name"`                            // 群控h5任务名称
	URL               string `gorm:"column:url"`                                  // 魔方0转正活动链接
	IsFlow            int    `gorm:"column:is_flow"`                              // 是否分流 0不分流, 1分流
	FlowBusiness      int    `gorm:"column:flow_business"`                        // 是否分流 分流业务线 1:K9商务一组 2:K10
	STaskID           int    `gorm:"column:s_task_id"`                            // 群控h5任务ID
	STaskName         string `gorm:"column:s_task_name"`                          // 群控h5任务名称
	ATaskID           int    `gorm:"column:a_task_id"`                            // 群控h5任务ID
	ATaskName         string `gorm:"column:a_task_name"`                          // 群控h5任务名称
	B1TaskID          int    `gorm:"column:b1_task_id"`                           // 群控h5任务ID
	B1TaskName        string `gorm:"column:b1_task_name"`                         // 群控h5任务名称
	B2TaskID          int    `gorm:"column:b2_task_id"`                           // 群控h5任务ID
	B2TaskName        string `gorm:"column:b2_task_name"`                         // 群控h5任务名称
	CTaskID           int    `gorm:"column:c_task_id"`                            // 群控h5任务ID
	CTaskName         string `gorm:"column:c_task_name"`                          // 群控h5任务名称
	UnknownTaskID     int    `gorm:"column:unknown_task_id"`                      // 群控h5任务ID
	UnknownTaskName   string `gorm:"column:unknown_task_name"`                    // 群控h5任务名称
	IsNew             int    `gorm:"column:is_new"`                               // 是否新活动
	Deleted           int    `gorm:"column:deleted"`                              // 0未删除, 1删除
	OperatorID        ID     `gorm:"column:operator_id"`                          // 操作人ID
	OperatorName      string `gorm:"column:operator_name"`                        // 操作人姓名
	CreateTime        int    `gorm:"column:create_time;autoCreateTime;<-:create"` // 创建时间
	UpdateTime        int    `gorm:"column:update_time;autoUpdateTime"`           // 更新时间
}

func (m *ZeroActivity) TableName() string {
	return "tblZeroActivity"
}
