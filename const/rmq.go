package constant

const (
	//售卖订单
	TagID174101 = "174101" //订单生成
	TagID174102 = "174102" //未支付关单
	TagID174103 = "174103" //神农 支付成功
	TagID174105 = "174105" //成单
	TagID174109 = "174109" //售后服务
	TagID174113 = "174113" //后置地址订单添加地址通知
	TagID700025 = "700025" //新 订单入交易分析 go-toufang生产

	TagID500011 = "500011" //免登陆信令
	TagID500024 = "500024" //魔方限购
	TagID500009 = "500009" //通知电销

	TagID500031 = "500031" //F0接口访问耗时

	TagID101000   = "101000"   //抖音回调 下单
	TagID101000V2 = "101000V2" //抖音回调 成单 自建链路
	TagID206000   = "206000"   //抖音回调 退款成功
	TagID101001   = "101001"   //抖音回调 成单 三方卡券

	TagIDDelayAicall      = "delayAicall"      //支付成功后 延迟10min ai call + sms
	TagIDDelaySmscall     = "delaySmscall"     //下单未支付 延迟2min ai call + sms
	TagIDDelayEditAddress = "delayEditAddress" //填写下单地址后信令通知
	TagIDTblH5SportForm   = "tblH5SportForm"
	TagIDSaveAddress      = "saveAddress"            //保存地址
	TagIDOrderReportZooms = "orderReportZooms"       //订单上报到zooms service
	TagIDDyOrderCreate    = "dyAfterLiveOrderCreate" //短信后链路抖音订单入表
	TagIDSyncSmsResult    = "syncSmsResult"          //抖店云短信回执
	TagIDDyxzBindOrderRel = "dyxzBindOrderRel"       //小鹿页面，异步关联订单
	TagIDSyncDecryptLog   = "syncDecryptLog"         //解密记录表数据同步
	TagIDPushXcDyOrder    = "pushXcDyOrder"          //星橙推送的抖音订单
	TagIDReportMarket     = "reportMarket"           //上报媒体点位
	TagIDPushXhsClue      = "pushXhsClue"            //小红书推送留资线索
	TagIDPushBdClue       = "pushBdClue"             //百度推送留资线索

	TagID666666         string = "666666"             //上报到字节的限购uaip
	TagID666667         string = "666667"             //上报到腾讯云种子包
	TagID777777         string = "777777"             //延迟计算文件的总行数
	TagIDTalentChange          = "talentPersonChange" //达人变动 新增、编辑、删除
	TagIDLiveCostChange        = "liveCostChange"     //录入花费变动 新增、删除

	TagIDUserFirstStartapp string = "$USER_FIRST_STARTAPP_IDFA$"

	//OA
	TagIDSupplierApply string = "supplierApply" //OA供应商申请
	TagIDContractApply string = "HT-ZCHT"
	TagIDSettleSubmit         = "FQ-FXHTDG" //结算单提交
	TagIDSettleStatus         = "FQ-FXHTDG" //结算流程终态

	//RTA
	TagID500040       string = "500040"       //rta-添加lastfrom，账号检查并绑定策略
	TagIDRtaRtExclude string = "rtaRtExclude" //rta实时排除逻辑
	TagID158888       string = "158888"       //rta批量处理设备号
	TagID158889       string = "158889"       //rta维护引擎侧设备
	TagID158890       string = "158890"       //rta全量设备号续期ttl

	TagIDCallbackShow string = "callbackShow" //回调曝光
)
