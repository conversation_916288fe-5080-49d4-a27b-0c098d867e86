package tfmodels

type QudaoExternalDySms struct {
	ID         int64  `gorm:"column:id" json:"id"`                                    // 主键id
	Channel    string `gorm:"column:channel" json:"channel"`                          // 渠道
	Type       int    `gorm:"column:type" json:"type"`                                // 类型 1:地址填写短信 2:物流短信;3自动填写地址;4地址填写完成确后短信
	Source     int    `gorm:"column:source" json:"source"`                            // 数据来源：1星橙;2自解密
	OrderID    string `gorm:"column:order_id" json:"order_id"`                        // 订单id
	ShopID     string `gorm:"column:shop_id" json:"shop_id"`                          // 店铺id
	SyncID     string `gorm:"column:sync_id" json:"sync_id"`                          // 短信模板id
	Status     int    `gorm:"column:status" json:"status"`                            // 发送状态 1成功；2失败
	Params     string `gorm:"column:params" json:"params"`                            // params
	Ext        string `gorm:"column:ext" json:"ext"`                                  // json扩展字段
	CreateTime int64  `gorm:"create_time;autoCreateTime;<-:create" json:"createTime"` // 创建时间
	UpdateTime int64  `gorm:"column:update_time" json:"update_time"`                  // 更新时间
	Deleted    int    `gorm:"column:deleted" json:"deleted"`                          // 是否删除
}

// TableName 设置表名
func (QudaoExternalDySms) TableName() string {
	return "tblQudaoExternalDySms"
}
