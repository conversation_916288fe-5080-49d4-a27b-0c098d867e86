package tfmodels

type ZeroActivityDownload struct {
	Id           uint64 `gorm:"column:id" json:"id"`                       // id
	ActId        int64  `gorm:"column:act_id" json:"actId"`                // 活动id
	FileName     string `gorm:"column:file_name" json:"fileName"`          // 文件名称
	FileUrl      string `gorm:"column:file_url" json:"fileUrl"`            // 文件url
	FileDataSize int64  `gorm:"column:file_data_size" json:"fileDataSize"` // 文件数据量
	Status       int8   `gorm:"column:status" json:"status"`               // 状态 1-未开始 2-进行中 3-已结束
	ResultMsg    string `gorm:"column:result_msg" json:"resultMsg"`        // 结果详情
	Source       int    `gorm:"column:source" json:"source"`               // 下载来源：1-河图，2-金丝雀
	OperatorID   int    `gorm:"column:operator_id" json:"operatorId"`      // 代理商ID
	AppID        int64  `gorm:"column:app_id" json:"appId"`                // 应用方ID
	InstID       int64  `gorm:"column:inst_id" json:"instId"`              // 机构ID
	CreateAt     string `gorm:"column:create_at" json:"createAt"`          // 创建人
	CreateTime   int64  `gorm:"column:create_time" json:"createTime"`      // 创建时间
	Deleted      int8   `gorm:"column:deleted" json:"deleted"`             // 是否删除 0-否 1-是
	Ext          string `gorm:"column:ext" json:"ext"`                     // 扩展字段 存下载条件
}

func (ZeroActivityDownload) TableName() string {
	return TblZeroActivityDownload
}
