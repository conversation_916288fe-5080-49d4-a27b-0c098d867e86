package fenxiaoconsts

const (
	// 0元活动方，是否新数据
	IsNewNo  = 0
	IsNewYes = 1

	// 0元活动投放链接，是否新数据枚举
	DetailIsNewNo     = 0 // 历史活动历史创建的投放链接
	DetailIsNewYesSys = 1 // 新活动且系统创建的投放链接
	DetailIsNewYesQk  = 2 // 新旧活动在群控创建的新投放链接

	// 活动类型
	ZeroActTypeQkMf = 1 // 群控魔方
	ZeroActTypeQkZt = 2 // 群控直投

	ZeroActDownloadSourceHetu   = 1 // 河图
	ZeroActDownloadSourceAfxmis = 2 // 金丝雀

	ZeroActLinkStatusOpen   = 0  // 开启状态
	ZeroActLinkStatusClose  = 1  // 关闭状态
	ZeroActLinkStatusUnKnow = -1 // 未知状态

	ZeroActChannelStatusOpen   = 0  // 开启状态
	ZeroActChannelStatusClose  = 1  // 关闭状态
	ZeroActChannelStatusUnKnow = -1 // 未知状态

	// 0元投放链接生成状态, 即 tblZeroActivityGenLinkTask 和 tblZeroActivityGenLinkDetail 的状态
	ZeroActGenLinkStatusInit = 0 // 初始态
	ZeroActGenLinkStatusRun  = 1 // 执行中
	ZeroActGenLinkStatusFail = 2 // 失败
	ZeroActGenLinkStatusSucc = 3 // 成功

	// 0元投放链接动作生成状态， 即 tblZeroActivityGenLinkEvent.status
	ZeroActGenLinkEventStatusInit                     = 0  // 初始状态
	ZeroActGenLinkEventStatusChannelCreateFail        = 1  // 创建渠道失败
	ZeroActGenLinkEventStatusChannelCreateSuccess     = 2  // 创建渠道成功
	ZeroActGenLinkEventStatusPromotePlanCreateFail    = 3  // 创建投放计划失败
	ZeroActGenLinkEventStatusPromotePlanCreateSuccess = 4  // 创建投放计划成功
	ZeroActGenLinkEventStatusChannelBindFail          = 5  // H5和绑定渠道失败
	ZeroActGenLinkEventStatusChannelBindSuccess       = 6  // H5和绑定渠道成功
	ZeroActGenLinkEventStatusAppletCreateFail         = 7  // 创建小程序外链失败
	ZeroActGenLinkEventStatusAppletCreateSuccess      = 8  // 创建小程序外链成功
	ZeroActGenLinkEventStatusFinalSuccess             = 99 // 最终成功状态

	// 线上群控渠道大类定义常量
	ChannelClassPBDOnline = 645
	ChannelClassCSOnline  = 328
	ChannelClassCPSOnline = 323
	ChannelClassSQOnline  = 320
	ChannelClassJXOnline  = 317

	// 线下群控渠道大类定义常量
	ChannelClassCSTest  = 328
	ChannelClassCPSTest = 35
	ChannelClassSQTest  = 32
	ChannelClassJXTest  = 29
)

var ChannelClassMapOnline = map[int]string{
	ChannelClassPBDOnline: "PBD",
	ChannelClassCSOnline:  "测试",
	ChannelClassCPSOnline: "CPS",
	ChannelClassSQOnline:  "社群",
	ChannelClassJXOnline:  "进校",
}

var ChannelClassMapTest = map[int]string{
	ChannelClassCSTest:  "测试",
	ChannelClassCPSTest: "CPS",
	ChannelClassSQTest:  "社群",
	ChannelClassJXTest:  "进校",
}

const (
	ChannelNameAbbrQZ = "qz"
	ChannelNameAbbrA  = "a"
	ChannelNameAbbrB  = "b"
	ChannelNameAbbrC  = "c"
	ChannelNameAbbrE  = "e"
	ChannelNameAbbrF  = "f"
	ChannelNameAbbrH  = "h"
)

var ChannelNameAbbrMap = map[string]string{
	ChannelNameAbbrQZ: "qz（群转）",     // 群转
	ChannelNameAbbrA:  "a（0转正小学）", // 0转正小学
	ChannelNameAbbrB:  "b（0转正初中）", // 0转正初中
	ChannelNameAbbrC:  "c（单高中）",    // 单高中
	ChannelNameAbbrE:  "e（小初高）",    // 小初高
	ChannelNameAbbrF:  "f（小初）",      // 小初
	ChannelNameAbbrH:  "h（初高）",      // 初高
}
