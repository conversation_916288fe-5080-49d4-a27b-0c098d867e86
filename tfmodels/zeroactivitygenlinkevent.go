package tfmodels

type ZeroActivityGenLinkEvent struct {
	ID            uint64 `gorm:"column:id;primaryKey"`                        // 明细唯一标识
	TaskID        uint64 `gorm:"column:task_id"`                              // tblZeroActivityGenLinkTask主键id
	DetailID      uint64 `gorm:"column:detail_id"`                            // tblZeroActivityGenLinkDetail主键id
	ActID         int64  `gorm:"column:act_id"`                               // 活动ID
	ActType       int    `gorm:"column:act_type"`                             // 活动类型：1-群控魔方，2-群控直投
	H5TaskID      int    `gorm:"column:h5_task_id"`                           // 群控H5任务ID
	ChannelID     int64  `gorm:"column:channel_id"`                           // 群控渠道ID
	ChannelName   string `gorm:"column:channel_name"`                         // 群控渠道名称
	ClassId       int    `gorm:"column:class_id"`                             // 渠道大类
	LaunchID      int64  `gorm:"column:launch_id"`                            // 群控投放计划id
	Status        int    `gorm:"column:status"`                               // 明细状态：0-初始态，1-创建渠道失败，2-创建渠道成功, 3-投放计划创建失败，4-投放计划创建成功，4-H5和渠道绑定失败，5-H5和渠道绑定成功，6-生成小程序外链失败，7-生成小程序外链成功，99-完成
	ChannelStatus int    `gorm:"column:channel_status"`                       // 渠道状态：-1未知，0-开启，1关闭
	LinkStatus    int    `gorm:"column:link_status"`                          // 投放链接状态：-1未知，0-开启，1关闭
	LastRunTime   int    `gorm:"column:last_run_time"`                        // 上次执行时间
	RetryNum      int    `gorm:"column:retry_num"`                            // 重试次数
	CreateTime    int    `gorm:"column:create_time;autoCreateTime;<-:create"` // 创建时间
	UpdateTime    int    `gorm:"column:update_time;autoUpdateTime"`           // 更新时间
	Deleted       int    `gorm:"column:deleted"`                              // 是否删除，0-否，1-是
	ErrMsg        string `gorm:"column:err_msg"`                              // 错误信息（仅在失败时记录）
	Ext           string `gorm:"column:ext"`                                  // 扩展字段
}

func (m *ZeroActivityGenLinkEvent) TableName() string {
	return TblZeroActivityGenLinkEvent
}
