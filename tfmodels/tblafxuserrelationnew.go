package tfmodels

type AFXUserRelationNew struct {
	ID               int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`      // 主键
	InstID           int64  `gorm:"column:inst_id" json:"instId"`                      // 河图所属机构ID
	AppID            int64  `gorm:"column:app_id" json:"appId"`                        // 应用方id
	PromoteLv1       int    `gorm:"column:promote_lv1" json:"promoteLv1"`              // 推广产品一级ID-旧
	PromoteBusiness  int    `gorm:"column:promote_business" json:"promoteBusiness"`    // 伏羲机构报备-业务线
	PromoteGradeDept int    `gorm:"column:promote_grade_dept" json:"promoteGradeDept"` // 伏羲机构报备-学部
	PromoteLv1New    int    `gorm:"column:promote_lv1_new" json:"promoteLv1New"`       // 伏羲机构报备-新推广产品
	Cadre            string `gorm:"column:cadre" json:"cadre"`                         // 归属人
	StartTime        int64  `gorm:"column:start_time" json:"startTime"`                // 关系绑定时间戳
	EndTime          int64  `gorm:"column:end_time" json:"endTime"`                    // 关系解除时间戳,0-代表未解除，无限长
	Status           int    `gorm:"column:status" json:"status"`                       // 状态:1->生效, 2->失效，3-释放公海
	CreateTime       int64  `gorm:"column:create_time;autoCreateTime;<-:create"`       // 创建时间
	UpdateTime       int64  `gorm:"column:update_time;autoUpdateTime"`                 // 更新时间
	FlowTypeID       int64  `gorm:"column:flow_type_id" json:"flowTypeId"`             // 流量类型ID，根据新推广产品+学部计算出来的
}

// TableName 指定表名
func (AFXUserRelationNew) TableName() string {
	return TblAfxUserRelationNew
}

const (
	TblUserRelationNewStatusUsed    = 1 // 当前表中数据生效中
	TblUserRelationNewStatusUnused  = 2 // 表数据已失效
	TblUserRelationNewStatusRelease = 3 // 表数据已释放
)
