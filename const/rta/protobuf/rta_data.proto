syntax = "proto3";

option go_package = "../protobuf/";

message RtaData {
    repeated uint32 dx_list = 1 [packed=true];
    repeated uint32 rt_ex_list = 2 [packed=true];
    PriceInfo priceInfo = 4;

    map<string, RtaShowInfo> show_info = 5;
    map<string, int64> rt_ex_tags = 6;
}

message PriceInfo {
    float pltv30 = 1;
    float pcvr = 2;
    int32 grade_id = 4;
    int32 grade_id_v2 = 5;
    float pratio = 6;
    int32 v = 7;
    float cvr_level = 8;
}

message RtaShowInfo {
    int64 date = 1;
    int32 num = 2;
}