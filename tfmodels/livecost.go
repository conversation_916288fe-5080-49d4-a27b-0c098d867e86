package tfmodels

type LiveCost struct {
	ID               int64  `gorm:"column:id;primary_key"`                       // 主键
	CostDate         int    `gorm:"column:cost_date"`                            // 日期 20160102
	Business         string `gorm:"column:business"`                             // 业务线
	Plat             int    `gorm:"column:plat"`                                 // 合作平台：1抖音，2快手，3视频号，4淘宝
	CooperateMode    int    `gorm:"column:cooperate_mode"`                       // 合作形式： 1达播，2自播
	OperatorMode     int    `gorm:"column:operator_mode"`                        // 运营形式：1自运营，2代运营
	AwemeID          string `gorm:"column:aweme_id"`                             // 达人ID
	AwemeName        string `gorm:"column:aweme_name"`                           // 达人名称
	RoomID           string `gorm:"column:room_id"`                              // 直播间ID
	AwemeType        int    `gorm:"column:aweme_type"`                           // 达人类型：1代理商，2个人
	InstID           int64  `gorm:"column:inst_id"`                              // 机构代理ID
	InstName         string `gorm:"column:inst_name"`                            // 机构代理Name
	Account          string `gorm:"column:account"`                              // 账号(伏羲-媒体账号)
	MarketingGoal    int    `gorm:"column:marketing_goal"`                       // 推广形式：1直播形式，2短视频形式
	CostSource       int    `gorm:"column:cost_source"`                          // 花费来源：1系统生成，2人工录入
	AdCost           int64  `gorm:"column:ad_cost"`                              // 投放账面花费（单位 元*100000）
	CompensationCost int64  `gorm:"column:compensation_cost"`                    // 投放赔付花费（单位 元*100000）
	CashCost         int64  `gorm:"column:cash_cost"`                            // 投放现金花费（单位 元*100000）
	LiveCost         int64  `gorm:"column:live_cost"`                            // 直播费用（单位 元*100000）
	PlatformCost     int64  `gorm:"column:platform_cost"`                        // 平台服务费（单位 元*100000）
	GmvCommission    int64  `gorm:"column:gmv_commission"`                       // GMV分佣（单位 元*100000）
	OtherCosts       int64  `gorm:"column:other_costs"`                          // 其他费用（单位 元*100000）
	TotalCost        int64  `gorm:"column:total_cost"`                           // 总花费（单位 元*100000）
	InstRebateCost   int64  `gorm:"column:inst_rebate_cost"`                     // 代理商返点金额（单位 元*100000）
	FrameCost        int64  `gorm:"column:frame_cost"`                           // 媒体框返费用（单位 元*100000）
	Operator         string `gorm:"column:operator"`                             // 操作人
	Owner            string `gorm:"owner" json:"owner"`                          // 归属人
	OperatorTime     int    `gorm:"column:operator_time"`                        // 编辑时间
	Deleted          int    `gorm:"column:deleted"`                              // 0未删除，1已删除
	UpdateTime       int    `gorm:"column:update_time;autoUpdateTime"`           // 更新时间
	CreateTime       int    `gorm:"column:create_time;autoCreateTime;<-:create"` // 创建时间
}

func (m *LiveCost) TableName() string {
	return TblLiveCost
}
