package rta

const (
	CallbackShowDays = 1
)

// RtaPortraitPbInfo 画像Pb-json最终存储结构
type RtaPortraitPbInfo struct {
	DxList          []uint32               `json:"dx_list"`              // 定向包含tag列
	RtExList        []uint32               `json:"rt_ex_list,omitempty"` // 实时排除tag列表
	RtExTags        map[string]int64       `json:"rt_ex_tags"`
	RtaBidPriceInfo *RtaBidPriceInfo       `json:"priceInfo"` // 出价特征列表
	ShowInfo        map[string]RtaShowInfo `json:"showInfo"`
}

type RtaBidPriceInfo struct {
	Pltv30    float32 `json:"pltv30"`
	Cvr       float32 `json:"pcvr"`
	GradeId   int32   `json:"grade_id"`
	GradeIdV2 int32   `json:"grade_id_v2"`
	Pratio    float32 `json:"pratio"`
	Version   int32   `json:"v"`
	CvrLevel  float32 `json:"cvr_level"`
}

type RtaShowInfo struct {
	Date int64 `json:"date"`
	Num  int32 `json:"num"`
}
