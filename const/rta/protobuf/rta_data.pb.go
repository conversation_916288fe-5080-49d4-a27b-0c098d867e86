// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v5.27.1
// source: rta_data.proto

package protobuf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RtaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DxList    []uint32                `protobuf:"varint,1,rep,packed,name=dx_list,json=dxList,proto3" json:"dx_list,omitempty"`
	RtExList  []uint32                `protobuf:"varint,2,rep,packed,name=rt_ex_list,json=rtExList,proto3" json:"rt_ex_list,omitempty"`
	PriceInfo *PriceInfo              `protobuf:"bytes,4,opt,name=priceInfo,proto3" json:"priceInfo,omitempty"`
	ShowInfo  map[string]*RtaShowInfo `protobuf:"bytes,5,rep,name=show_info,json=showInfo,proto3" json:"show_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	RtExTags  map[string]int64        `protobuf:"bytes,6,rep,name=rt_ex_tags,json=rtExTags,proto3" json:"rt_ex_tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *RtaData) Reset() {
	*x = RtaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rta_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaData) ProtoMessage() {}

func (x *RtaData) ProtoReflect() protoreflect.Message {
	mi := &file_rta_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaData.ProtoReflect.Descriptor instead.
func (*RtaData) Descriptor() ([]byte, []int) {
	return file_rta_data_proto_rawDescGZIP(), []int{0}
}

func (x *RtaData) GetDxList() []uint32 {
	if x != nil {
		return x.DxList
	}
	return nil
}

func (x *RtaData) GetRtExList() []uint32 {
	if x != nil {
		return x.RtExList
	}
	return nil
}

func (x *RtaData) GetPriceInfo() *PriceInfo {
	if x != nil {
		return x.PriceInfo
	}
	return nil
}

func (x *RtaData) GetShowInfo() map[string]*RtaShowInfo {
	if x != nil {
		return x.ShowInfo
	}
	return nil
}

func (x *RtaData) GetRtExTags() map[string]int64 {
	if x != nil {
		return x.RtExTags
	}
	return nil
}

type PriceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pltv30    float32 `protobuf:"fixed32,1,opt,name=pltv30,proto3" json:"pltv30,omitempty"`
	Pcvr      float32 `protobuf:"fixed32,2,opt,name=pcvr,proto3" json:"pcvr,omitempty"`
	GradeId   int32   `protobuf:"varint,4,opt,name=grade_id,json=gradeId,proto3" json:"grade_id,omitempty"`
	GradeIdV2 int32   `protobuf:"varint,5,opt,name=grade_id_v2,json=gradeIdV2,proto3" json:"grade_id_v2,omitempty"`
	Pratio    float32 `protobuf:"fixed32,6,opt,name=pratio,proto3" json:"pratio,omitempty"`
	V         int32   `protobuf:"varint,7,opt,name=v,proto3" json:"v,omitempty"`
	CvrLevel  float32 `protobuf:"fixed32,8,opt,name=cvr_level,json=cvrLevel,proto3" json:"cvr_level,omitempty"`
}

func (x *PriceInfo) Reset() {
	*x = PriceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rta_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PriceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PriceInfo) ProtoMessage() {}

func (x *PriceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rta_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PriceInfo.ProtoReflect.Descriptor instead.
func (*PriceInfo) Descriptor() ([]byte, []int) {
	return file_rta_data_proto_rawDescGZIP(), []int{1}
}

func (x *PriceInfo) GetPltv30() float32 {
	if x != nil {
		return x.Pltv30
	}
	return 0
}

func (x *PriceInfo) GetPcvr() float32 {
	if x != nil {
		return x.Pcvr
	}
	return 0
}

func (x *PriceInfo) GetGradeId() int32 {
	if x != nil {
		return x.GradeId
	}
	return 0
}

func (x *PriceInfo) GetGradeIdV2() int32 {
	if x != nil {
		return x.GradeIdV2
	}
	return 0
}

func (x *PriceInfo) GetPratio() float32 {
	if x != nil {
		return x.Pratio
	}
	return 0
}

func (x *PriceInfo) GetV() int32 {
	if x != nil {
		return x.V
	}
	return 0
}

func (x *PriceInfo) GetCvrLevel() float32 {
	if x != nil {
		return x.CvrLevel
	}
	return 0
}

type RtaShowInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date int64 `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	Num  int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *RtaShowInfo) Reset() {
	*x = RtaShowInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_rta_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RtaShowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RtaShowInfo) ProtoMessage() {}

func (x *RtaShowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rta_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RtaShowInfo.ProtoReflect.Descriptor instead.
func (*RtaShowInfo) Descriptor() ([]byte, []int) {
	return file_rta_data_proto_rawDescGZIP(), []int{2}
}

func (x *RtaShowInfo) GetDate() int64 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *RtaShowInfo) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

var File_rta_data_proto protoreflect.FileDescriptor

var file_rta_data_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x72, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe5, 0x02, 0x0a, 0x07, 0x52, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x07,
	0x64, 0x78, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x64, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0a, 0x72, 0x74, 0x5f,
	0x65, 0x78, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x72, 0x74, 0x45, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x09, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x52, 0x74, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x0a, 0x72, 0x74,
	0x5f, 0x65, 0x78, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x52, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x74, 0x45, 0x78, 0x54, 0x61, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x72, 0x74, 0x45, 0x78, 0x54, 0x61, 0x67, 0x73,
	0x1a, 0x49, 0x0a, 0x0d, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x52, 0x74, 0x61, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x52,
	0x74, 0x45, 0x78, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb5, 0x01, 0x0a, 0x09, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6c, 0x74, 0x76, 0x33, 0x30,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x70, 0x6c, 0x74, 0x76, 0x33, 0x30, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x63, 0x76, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x63,
	0x76, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0b, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x76, 0x32, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x56, 0x32, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x70,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x0c, 0x0a, 0x01, 0x76, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x76, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x76, 0x72, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x63, 0x76, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x22, 0x33, 0x0a, 0x0b, 0x52, 0x74, 0x61, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6e, 0x75, 0x6d, 0x42, 0x0e, 0x5a, 0x0c, 0x2e, 0x2e, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rta_data_proto_rawDescOnce sync.Once
	file_rta_data_proto_rawDescData = file_rta_data_proto_rawDesc
)

func file_rta_data_proto_rawDescGZIP() []byte {
	file_rta_data_proto_rawDescOnce.Do(func() {
		file_rta_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_rta_data_proto_rawDescData)
	})
	return file_rta_data_proto_rawDescData
}

var file_rta_data_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_rta_data_proto_goTypes = []interface{}{
	(*RtaData)(nil),     // 0: RtaData
	(*PriceInfo)(nil),   // 1: PriceInfo
	(*RtaShowInfo)(nil), // 2: RtaShowInfo
	nil,                 // 3: RtaData.ShowInfoEntry
	nil,                 // 4: RtaData.RtExTagsEntry
}
var file_rta_data_proto_depIdxs = []int32{
	1, // 0: RtaData.priceInfo:type_name -> PriceInfo
	3, // 1: RtaData.show_info:type_name -> RtaData.ShowInfoEntry
	4, // 2: RtaData.rt_ex_tags:type_name -> RtaData.RtExTagsEntry
	2, // 3: RtaData.ShowInfoEntry.value:type_name -> RtaShowInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_rta_data_proto_init() }
func file_rta_data_proto_init() {
	if File_rta_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_rta_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rta_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PriceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_rta_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RtaShowInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rta_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rta_data_proto_goTypes,
		DependencyIndexes: file_rta_data_proto_depIdxs,
		MessageInfos:      file_rta_data_proto_msgTypes,
	}.Build()
	File_rta_data_proto = out.File
	file_rta_data_proto_rawDesc = nil
	file_rta_data_proto_goTypes = nil
	file_rta_data_proto_depIdxs = nil
}
