package tfmodels

type ZeroActivityGenLinkTask struct {
	ID            uint64 `gorm:"column:id;primaryKey"`                        // 任务唯一标识
	ActID         int64  `gorm:"column:act_id"`                               // 活动ID
	Count         int    `gorm:"column:count"`                                // 生成链接数量
	Dt            string `gorm:"column:dt"`                                   // 生成日期
	ChannelNumMin int    `gorm:"column:channel_num_min"`                      // 渠道编号最小值
	ChannelNumMax int    `gorm:"column:channel_num_max"`                      // 渠道编号最大值
	Status        int    `gorm:"column:status"`                               // 任务状态：0-初始态，1-生成中，2-生成成功，3-失败
	OperatorId    int    `gorm:"column:operator_id"`                          // 操作用户ID
	AppId         int    `gorm:"column:app_id"`                               // 操作用户所属应用方
	InstId        int    `gorm:"column:inst_id"`                              // 操作用户所属机构
	CreateTime    int    `gorm:"column:create_time;autoCreateTime;<-:create"` // 创建时间
	UpdateTime    int    `gorm:"column:update_time;autoUpdateTime"`           // 更新时间
	Deleted       int    `gorm:"column:deleted"`                              // 是否删除，0-否，1-是
	Msg           string `gorm:"column:msg"`                                  // 错误信息（仅在失败时记录）
	Ext           string `gorm:"column:ext"`                                  // 扩展字段
}

func (m *ZeroActivityGenLinkTask) TableName() string {
	return TblZeroActivityGenLinkTask
}
