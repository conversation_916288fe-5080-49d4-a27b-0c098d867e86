package utils

// 切片字符串包含
func ContainStr(arr []string, str string) bool {
	if nil == arr {
		return false
	}
	for _, s := range arr {
		if s == str {
			return true
		}
	}
	return false
}

// 切片int包含
func ContainInt(arr []int, num int) bool {
	if nil == arr {
		return false
	}
	for _, s := range arr {
		if s == num {
			return true
		}
	}
	return false
}

// 切片字符串 去重
func DuplicateStr(arr []string) []string {
	seen := make(map[string]struct{})
	result := make([]string, 0)
	for i := range arr {
		if _, ok := seen[arr[i]]; !ok {
			result = append(result, arr[i])
			seen[arr[i]] = struct{}{}
		}
	}

	return result
}

func ChunkInt64(t []int64, size int) (chunks [][]int64) {
	items := make([]int64, len(t))
	for k, id := range t {
		items[k] = id
	}
	for size < len(items) {
		items, chunks = items[size:], append(chunks, items[0:size:size])
	}

	return append(chunks, items)
}

func ChunkString(t []string, size int) (chunks [][]string) {
	items := make([]string, len(t))
	for k, id := range t {
		items[k] = id
	}
	for size < len(items) {
		items, chunks = items[size:], append(chunks, items[0:size:size])
	}

	return append(chunks, items)
}

func ChunkUint64(t []uint64, size int) (chunks [][]uint64) {
	items := make([]uint64, len(t))
	for k, id := range t {
		items[k] = id
	}
	for size < len(items) {
		items, chunks = items[size:], append(chunks, items[0:size:size])
	}

	return append(chunks, items)
}

func ChunkInt(t []int, size int) (chunks [][]int) {
	items := make([]int, len(t))
	for k, id := range t {
		items[k] = id
	}
	for size < len(items) {
		items, chunks = items[size:], append(chunks, items[0:size:size])
	}

	return append(chunks, items)
}
