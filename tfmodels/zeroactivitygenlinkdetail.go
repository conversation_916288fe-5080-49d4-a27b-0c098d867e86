package tfmodels

type ZeroActivityGenLinkDetail struct {
	ID            uint64 `gorm:"column:id;primaryKey"`                        // 明细唯一标识
	TaskID        uint64 `gorm:"column:task_id"`                              // tblZeroActivityGenLinkTask主键id
	ActID         int64  `gorm:"column:act_id"`                               // 活动ID
	ActType       int    `gorm:"column:act_type"`                             // 活动类型：1-群控魔方，2-群控直投
	H5TaskIDs     string `gorm:"column:h5_task_ids"`                          // 群控H5任务ID，逗号隔开
	ChannelID     int64  `gorm:"column:channel_id"`                           // 群控渠道ID
	ChannelName   string `gorm:"column:channel_name"`                         // 群控渠道名称
	ChannelNum    int    `gorm:"column:channel_num"`                          // 渠道编号
	ClassId       int    `gorm:"column:class_id"`                             // 渠道大类
	Status        int    `gorm:"column:status"`                               // 子任务状态：0-初始态，1-执行中，2-执行失败，3-执行成功
	ChannelStatus int    `gorm:"column:channel_status"`                       // 渠道状态：-1未知，0-开启，1关闭
	LinkStatus    int    `gorm:"column:link_status"`                          // 投放链接状态：-1未知，0-开启，1关闭
	InstId        int    `gorm:"column:inst_id"`                              // 代理商机构id
	IsNew         int    `gorm:"column:is_new"`                               // 是否新数据，0-历史活动历史创建的投放链接，1-新活动且系统创建的投放链接，2-新旧活动在群控创建的新投放链接
	OperatorID    int    `gorm:"column:operator_id"`                          // 操作人ID-代理商ID
	Deleted       int    `gorm:"column:deleted"`                              // 是否删除，0-否，1-是
	CreateTime    int    `gorm:"column:create_time;autoCreateTime;<-:create"` // 创建时间
	UpdateTime    int    `gorm:"column:update_time;autoUpdateTime"`           // 更新时间
	ErrMsg        string `gorm:"column:err_msg"`                              // 错误信息（仅在失败时记录）
	Ext           string `gorm:"column:ext"`                                  // 扩展字段
}

func (m *ZeroActivityGenLinkDetail) TableName() string {
	return TblZeroActivityGenLinkDetail
}
