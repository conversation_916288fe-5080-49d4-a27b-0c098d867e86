package tfmodels

const (
	ExternalDyOrderSourceXc   = 1 //星橙推送的订单
	ExternalDyOrderSourceSelf = 2 //自建的解密订单
)

type QudaoExternalDyOrder struct {
	ID               int64  `gorm:"column:id" json:"id"`                                    // 主键id
	Channel          string `gorm:"column:channel" json:"channel"`                          // 渠道
	Source           int    `gorm:"column:source" json:"source"`                            // 数据来源：1星橙;2自解密
	OrderID          string `gorm:"column:order_id" json:"order_id"`                        // 订单id
	ShopID           string `gorm:"column:shop_id" json:"shop_id"`                          // 店铺id
	Tel              string `gorm:"tel" json:"tel"`                                         // 加密手机号
	ProductID        string `gorm:"column:product_id" json:"product_id"`                    // 商品id
	ProductName      string `gorm:"column:product_name" json:"product_name"`                // 商品名称
	OrderTime        int64  `gorm:"column:order_time" json:"order_time"`                    // 下单时间
	OrderAmount      string `gorm:"column:order_amount" json:"order_amount"`                // 下单金额 字符串
	ZybUserID        int64  `gorm:"column:zyb_user_id" json:"zyb_user_id"`                  // zyb uid
	ZybOrderID       int64  `gorm:"column:zyb_order_id" json:"zyb_order_id"`                // zyb订单id
	HasAddr          int    `gorm:"column:has_addr" json:"has_addr"`                        // 是否填写地址 1已填写
	SaveAddrTime     int64  `gorm:"column:save_addr_time" json:"saveAddrTime"`              // 填写地址时间
	ReportJST        int    `gorm:"column:report_jst" json:"reportJST"`                     // 地址上报到聚水潭 0未上报 1上报
	ReportJSTTime    int64  `gorm:"column:report_jst_time" json:"reportJstTime"`            // 上报聚水潭地址时间
	IsSendExpresssms int64  `gorm:"column:is_send_expresssms" json:"isSendExpresssms"`      // 是否发送物流短信 0未发送 1已发送
	Ext              string `gorm:"column:ext" json:"ext"`                                  // json扩展字段
	CreateTime       int64  `gorm:"create_time;autoCreateTime;<-:create" json:"createTime"` // 创建时间
	UpdateTime       int64  `gorm:"column:update_time" json:"update_time"`                  // 更新时间
	Deleted          int    `gorm:"column:deleted" json:"deleted"`                          // 是否删除
}

// TableName 设置表名
func (QudaoExternalDyOrder) TableName() string {
	return "tblQudaoExternalDyOrder"
}
